; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

; SBUS信号测试打印环境
[env:sbus_test]
platform = espressif32
board = seeed_xiao_esp32c3
framework = arduino
board_build.f_cpu = 160000000L
lib_deps =
	bolderflight/Bolder Flight Systems SBUS@^8.1.4
extra_scripts =
	merge_firmware.py
build_flags =
	-D ARDUINO_USB_MODE=1
	-D ARDUINO_USB_CDC_ON_BOOT=1
src_filter = -<*> +<main_sbus_in.cpp>

; IBUS信号测试打印环境
[env:ibus_test]
platform = espressif32
board = seeed_xiao_esp32c3
framework = arduino
board_build.f_cpu = 160000000L
lib_deps =
	bmellink/IBusBM@^1.1.4
extra_scripts =
	merge_firmware.py
build_flags =
	-D ARDUINO_USB_MODE=1
	-D ARDUINO_USB_CDC_ON_BOOT=1
src_filter = -<*> +<main_ibus_in.cpp>

; SBUS转IBUS转换环境
[env:sbus_to_ibus]
platform = espressif32
board = seeed_xiao_esp32c3
framework = arduino
board_build.f_cpu = 160000000L
lib_deps =
	bolderflight/Bolder Flight Systems SBUS@^8.1.4
	bmellink/IBusBM@^1.1.4
extra_scripts =
	merge_firmware.py
build_flags =
	-D ARDUINO_USB_MODE=1
	-D ARDUINO_USB_CDC_ON_BOOT=1
src_filter = -<*> +<main_sbus_to_ibus.cpp>
