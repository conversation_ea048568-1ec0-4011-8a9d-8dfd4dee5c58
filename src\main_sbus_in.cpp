#include <Arduino.h>
#include <HardwareSerial.h>
#include "sbus.h"

// SBUS输入测试打印程序

typedef struct
{
    int8_t sbus_rx_pin;
    int8_t sbus_tx_pin;
    int8_t led_pin;
} PinDefinition;

// 结构体实例化 - 独立的SBUS引脚
PinDefinition pin_definition = {
    .sbus_rx_pin = 20, // SBUS专用接收引脚
    .sbus_tx_pin = 21, // SBUS专用发送引脚（未使用）
    .led_pin = 8       // 状态指示LED
};

HardwareSerial SerialPrint(0); // USB串口用于调试输出
HardwareSerial SerialSbus(1);  // 硬件串口1用于SBUS

bfs::SbusRx sbus_rx(&SerialSbus, pin_definition.sbus_rx_pin, pin_definition.sbus_tx_pin, true);
bfs::SbusData sbus_data;

void setup()
{
    // 初始化USB串口用于调试输出
    Serial.begin(115200);
    pinMode(pin_definition.led_pin, OUTPUT);

    // 初始化SBUS通信
    sbus_rx.Begin();

    Serial.println("=== SBUS信号测试程序 ===");
    Serial.println("等待SBUS信号输入...");
    Serial.println("引脚配置:");
    Serial.print("  SBUS RX: GPIO");
    Serial.println(pin_definition.sbus_rx_pin);
    Serial.print("  LED: GPIO");
    Serial.println(pin_definition.led_pin);
    Serial.println("========================");
}

void loop()
{
    static unsigned long lastPrint = 0;
    static bool ledState = false;

    // 读取SBUS信号
    if (sbus_rx.Read())
    {
        sbus_data = sbus_rx.data();

        // LED指示数据接收
        ledState = !ledState;
        digitalWrite(pin_definition.led_pin, ledState);
    }

    // 每200ms打印一次数据
    if (millis() - lastPrint >= 500)
    {
        lastPrint = millis();

        Serial.print("SBUS通道: ");
        // 打印前8个通道
        for (int i = 0; i < 16; i++)
        {
            Serial.print("CH");
            Serial.print(i + 1);
            Serial.print(":");
            Serial.print(sbus_data.ch[i]);
            Serial.print(" ");
        }

        // 打印失联和故障安全状态
        Serial.print("| 失联:");
        Serial.print(sbus_data.lost_frame ? "是" : "否");
        Serial.print(" 故障安全:");
        Serial.print(sbus_data.failsafe ? "是" : "否");
        Serial.println();
    }
}
