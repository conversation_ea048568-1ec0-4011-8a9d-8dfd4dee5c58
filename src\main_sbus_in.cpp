#include <Arduino.h>
#include <HardwareSerial.h>
#include "sbus.h"

// SBUS输入测试打印程序
// 输入范围: 282~1722, 中位值: 1002
// 输出范围: 240~1808, 中位值: 1024

// SBUS信号转换函数
int convertSbusValue(int inputValue)
{
    // 输入范围参数
    const int INPUT_MIN = 282;
    const int INPUT_MAX = 1722;
    const int INPUT_CENTER = 1002;

    // 输出范围参数
    const int OUTPUT_MIN = 240;
    const int OUTPUT_MAX = 1808;
    const int OUTPUT_CENTER = 1024;

    // 限制输入值在有效范围内
    if (inputValue < INPUT_MIN)
        inputValue = INPUT_MIN;
    if (inputValue > INPUT_MAX)
        inputValue = INPUT_MAX;

    int outputValue;

    if (inputValue <= INPUT_CENTER)
    {
        // 处理低半段 (INPUT_MIN ~ INPUT_CENTER) -> (OUTPUT_MIN ~ OUTPUT_CENTER)
        outputValue = map(inputValue, INPUT_MIN, INPUT_CENTER, OUTPUT_MIN, OUTPUT_CENTER);
    }
    else
    {
        // 处理高半段 (INPUT_CENTER ~ INPUT_MAX) -> (OUTPUT_CENTER ~ OUTPUT_MAX)
        outputValue = map(inputValue, INPUT_CENTER, INPUT_MAX, OUTPUT_CENTER, OUTPUT_MAX);
    }

    return outputValue;
}

typedef struct
{
    int8_t sbus_rx_pin;
    int8_t sbus_tx_pin;
    int8_t led_pin;
} PinDefinition;

// 结构体实例化 - 独立的SBUS引脚
PinDefinition pin_definition = {
    .sbus_rx_pin = 20, // SBUS专用接收引脚
    .sbus_tx_pin = 21, // SBUS专用发送引脚
    .led_pin = 8       // 状态指示LED
};

HardwareSerial SerialPrint(0); // USB串口用于调试输出
HardwareSerial SerialSbus(1);  // 硬件串口1用于SBUS

bfs::SbusRx sbus_rx(&SerialSbus, pin_definition.sbus_rx_pin, pin_definition.sbus_tx_pin, true);
bfs::SbusTx sbus_tx(&SerialSbus, pin_definition.sbus_tx_pin, true);
bfs::SbusData sbus_data;
bfs::SbusData sbus_tx_data; // 用于发送的SBUS数据

void setup()
{
    // 初始化USB串口用于调试输出
    Serial.begin(115200);
    pinMode(pin_definition.led_pin, OUTPUT);

    // 初始化SBUS通信
    sbus_rx.Begin();
    sbus_tx.Begin();

    Serial.println("=== SBUS信号转换器程序 ===");
    Serial.println("等待SBUS信号输入...");
    Serial.println("引脚配置:");
    Serial.print("  SBUS RX: GPIO");
    Serial.println(pin_definition.sbus_rx_pin);
    Serial.print("  SBUS TX: GPIO");
    Serial.println(pin_definition.sbus_tx_pin);
    Serial.print("  LED: GPIO");
    Serial.println(pin_definition.led_pin);
    Serial.println("转换范围: 282~1722 -> 240~1808");
    Serial.println("中位值: 1002 -> 1024");
    Serial.println("============================");
}

void loop()
{
    static unsigned long lastPrint = 0;
    static bool ledState = false;

    // 读取SBUS信号
    if (sbus_rx.Read())
    {
        sbus_data = sbus_rx.data();

        // 转换所有通道数据
        for (int i = 0; i < 16; i++)
        {
            sbus_tx_data.ch[i] = convertSbusValue(sbus_data.ch[i]);
        }

        // 复制失联和故障安全状态
        sbus_tx_data.lost_frame = sbus_data.lost_frame;
        sbus_tx_data.failsafe = sbus_data.failsafe;

        // 设置转换后的数据并发送
        sbus_tx.data(sbus_tx_data);
        sbus_tx.Write();

        // LED指示数据接收和发送
        ledState = !ledState;
        digitalWrite(pin_definition.led_pin, ledState);
    }

    // 每500ms打印一次数据
    if (millis() - lastPrint >= 500)
    {
        lastPrint = millis();

        Serial.println("=== SBUS通道数据 ===");
        Serial.println("原始值 -> 转换值");

        // 打印前16个通道的原始值和转换后的值
        for (int i = 0; i < 16; i++)
        {
            int originalValue = sbus_data.ch[i];
            int convertedValue = sbus_tx_data.ch[i]; // 使用实际发送的转换值

            Serial.print("CH");
            if (i + 1 < 10)
                Serial.print(" "); // 对齐格式
            Serial.print(i + 1);
            Serial.print(": ");
            Serial.print(originalValue);
            Serial.print(" -> ");
            Serial.print(convertedValue);

            // 每4个通道换行，便于阅读
            if ((i + 1) % 4 == 0)
            {
                Serial.println();
            }
            else
            {
                Serial.print("  ");
            }
        }

        // 打印失联和故障安全状态
        Serial.print("状态 - 失联:");
        Serial.print(sbus_data.lost_frame ? "是" : "否");
        Serial.print(" | 故障安全:");
        Serial.print(sbus_data.failsafe ? "是" : "否");
        Serial.println();
        Serial.println("==================");
    }
}
